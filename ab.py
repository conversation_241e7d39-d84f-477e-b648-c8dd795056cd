import os
import re
import time
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import json
from datetime import datetime
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple
import asyncio
import aiohttp
from dataclasses import dataclass
import os
import requests
import json
from urllib.parse import urlparse
try:
    from playwright.sync_api import sync_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

@dataclass
class SitePattern:
    pattern_type: str
    confidence: float
    selectors: Dict[str, str]
    interaction_needs: Dict[str, bool]

@dataclass
class DocumentInfo:
    url: str
    title: str
    year: Optional[int]
    quarter: Optional[str]
    doc_type: str
    confidence: float

class ConfigManager:
    """Centralized configuration management"""
    
    def __init__(self, config_file: str = "companies_config.json"):
        self.config_file = config_file
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        try:
            with open(self.config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        return {
            "companies": {},
            "global_settings": {
                "max_retries": 3,
                "timeout_seconds": 30,
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "download_directory": "company_reports",
                "mapping_file": "filename_mappings.json",
                "min_file_size": 1000,
                "default_year_range": [2019, 2025],
                "confidence_threshold": 0.6
            },
            "filename_standards": {
                "pattern": "{company}_{document_type}_{year}_{quarter}.pdf",
                "date_format": "%Y-%m-%d %H:%M:%S"
            }
        }
    
    def get_company_config(self, company_name: str) -> Optional[Dict[str, Any]]:
        return self.config.get("companies", {}).get(company_name)
    
    def get_global_setting(self, key: str, default=None):
        return self.config.get("global_settings", {}).get(key, default)
    
    def get_all_companies(self) -> Dict[str, Dict[str, Any]]:
        return self.config.get("companies", {})
    
    def get_filename_pattern(self) -> str:
        return self.config.get("filename_standards", {}).get("pattern", "{company}_{document_type}_{year}_{quarter}.pdf")

class PatternDetector:
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.patterns = {
            'simple_list': {
                'indicators': ['direct PDF links in list/container'],
                'selectors': {'pdf_links': 'a[href$=".pdf"]', 'container': 'main, .content, body'},
                'confidence_threshold': 0.7
            },
            'table_based': {
                'indicators': ['PDFs organized in table format'],
                'selectors': {'pdf_links': 'table a[href$=".pdf"]', 'table': 'table'},
                'confidence_threshold': 0.8
            },
            'news_drill_down': {
                'indicators': ['news releases with PDF attachments'],
                'selectors': {'news_links': '.news a, .release a', 'pdf_links': 'a[href$=".pdf"]'},
                'confidence_threshold': 0.6
            },
            'categorized': {
                'indicators': ['documents in categories/tabs'],
                'selectors': {'categories': '.tab, .category, .section', 'pdf_links': 'a[href$=".pdf"]'},
                'confidence_threshold': 0.7
            }
        }
    
    def detect_pattern(self, url: str, soup: BeautifulSoup) -> SitePattern:
        scores = {}
        
        pdf_links = soup.find_all('a', href=lambda x: x and '.pdf' in x.lower())
        if len(pdf_links) >= 3:
            scores['simple_list'] = 0.8
        
        tables_with_pdfs = soup.find_all('table')
        table_pdf_count = 0
        for table in tables_with_pdfs:
            table_pdfs = table.find_all('a', href=lambda x: x and '.pdf' in x.lower())
            table_pdf_count += len(table_pdfs)
        
        if table_pdf_count >= 2:
            scores['table_based'] = 0.9
        
        news_indicators = soup.find_all(string=re.compile(r'news|press|release|earnings', re.I))
        if len(news_indicators) >= 3 and len(pdf_links) < 10:
            scores['news_drill_down'] = 0.7
        
        categories = soup.find_all(class_=re.compile(r'tab|category|section', re.I))
        if len(categories) >= 2:
            scores['categorized'] = 0.6
        
        if scores:
            best_pattern = max(scores.items(), key=lambda x: x[1])
            pattern_type, confidence = best_pattern
            
            return SitePattern(
                pattern_type=pattern_type,
                confidence=confidence,
                selectors=self.patterns[pattern_type]['selectors'],
                interaction_needs={'requires_js': False, 'needs_navigation': pattern_type == 'news_drill_down'}
            )
        
        return SitePattern(
            pattern_type='simple_list',
            confidence=0.5,
            selectors=self.patterns['simple_list']['selectors'],
            interaction_needs={'requires_js': False, 'needs_navigation': False}
        )

class DocumentAnalyzer:
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        
    def get_company_document_types(self, company_name: str) -> Dict[str, Dict[str, Any]]:
        """Get document type configuration for a specific company"""
        company_config = self.config_manager.get_company_config(company_name)
        if company_config:
            return company_config.get("document_types", {})
        return {}
    
    def get_company_year_range(self, company_name: str) -> Tuple[int, int]:
        """Get year range for a specific company"""
        company_config = self.config_manager.get_company_config(company_name)
        if company_config and "year_range" in company_config:
            year_range = company_config["year_range"]
            return year_range[0], year_range[1]
        
        default_range = self.config_manager.get_global_setting("default_year_range", [2019, 2025])
        return default_range[0], default_range[1]
    
    def _extract_year_from_filename(self, filename: str, min_year: int, max_year: int) -> Optional[int]:
        match = re.search(r'(20\d{2})', filename)
        if match:
            year = int(match.group(1))
            if min_year <= year <= max_year:
                return year
        match = re.search(r'fy\s?(\d{2})', filename, re.IGNORECASE)
        if match:
            year = int('20' + match.group(1))
            if min_year <= year <= max_year:
                return year
        return None

    def _extract_quarter_from_filename(self, filename: str) -> Optional[str]:
        filename = filename.lower()
        if re.search(r'\bq1\b|first.?quarter|1st.?quarter', filename):
            return 'Q1'
        elif re.search(r'\bq2\b|second.?quarter|2nd.?quarter', filename):
            return 'Q2'
        elif re.search(r'\bq3\b|third.?quarter|3rd.?quarter', filename):
            return 'Q3'
        elif re.search(r'\bq4\b|fourth.?quarter|4th.?quarter|year.?end|annual', filename):
            return 'Q4'
        return None

    def _extract_year(self, text: str, company_name: str, filename: str = "") -> Optional[int]:
        min_year, max_year = self.get_company_year_range(company_name)
        match = re.search(r'(20\d{2})', text)
        if match:
            year = int(match.group(1))
            if min_year <= year <= max_year:
                return year
        match = re.search(r'fy\s?(\d{2})', text, re.IGNORECASE)
        if match:
            year = int('20' + match.group(1))
            if min_year <= year <= max_year:
                return year
        if filename:
            return self._extract_year_from_filename(filename, min_year, max_year)
        return None

    def _extract_quarter(self, text: str, filename: str = "") -> Optional[str]:
        text_lower = text.lower()
        if re.search(r'\bq1\b|first.?quarter|1st.?quarter', text_lower):
            return 'Q1'
        elif re.search(r'\bq2\b|second.?quarter|2nd.?quarter', text_lower):
            return 'Q2'
        elif re.search(r'\bq3\b|third.?quarter|3rd.?quarter', text_lower):
            return 'Q3'
        elif re.search(r'\bq4\b|fourth.?quarter|4th.?quarter|year.?end|annual', text_lower):
            return 'Q4'
        if filename:
            return self._extract_quarter_from_filename(filename)
        return None

    def analyze_document(self, url: str, text: str, page_context: str = "", company_name: str = "") -> DocumentInfo:
        full_context = f"{url} {text} {page_context}".lower()
        filename = os.path.basename(url)
        doc_types = self.get_company_document_types(company_name)
        year = None
        quarter = None
        doc_type = None
        # Try to classify using config-driven rules
        for dtype, config in doc_types.items():
            # Check regex_patterns first
            for pattern in config.get("regex_patterns", []):
                if re.search(pattern, full_context, re.IGNORECASE):
                    doc_type = dtype
                    # Year extraction from regex match if possible
                    year_match = re.search(r'(20\\d{2})', full_context)
                    if year_match:
                        year = int(year_match.group(1))
                    break
            if doc_type:
                break
        # If not found by regex, try keywords
        if not doc_type:
            for dtype, config in doc_types.items():
                for kw in config.get("keywords", []):
                    if kw.lower() in full_context:
                        doc_type = dtype
                        break
                if doc_type:
                    break
        # Year extraction if not found
        if not year:
            year = self._extract_year(full_context, company_name, filename)
        # Quarter extraction
        if doc_type and doc_types.get(doc_type):
            config = doc_types[doc_type]
            if config.get("quarter_mapping", False):
                quarter = self._extract_quarter(full_context, filename)
            elif "quarter" in config:
                quarter = config["quarter"]
        if not quarter:
            quarter = self._extract_quarter(full_context, filename)
        # Fallback to generic classification if still unknown
        if not doc_type:
            doc_type = self._classify_document_type(full_context, year, quarter, company_name)
        confidence = self._calculate_confidence(url, text, year, quarter, doc_type, company_name)
        return DocumentInfo(
            url=url,
            title=text.strip(),
            year=year,
            quarter=quarter,
            doc_type=doc_type,
            confidence=confidence
        )
    
    def _classify_document_type(self, text: str, year: Optional[int], quarter: Optional[str], company_name: str) -> str:
        text_lower = text.lower()
        
        # Get company-specific document types and keywords
        doc_types = self.get_company_document_types(company_name)
        
        # Score each document type based on keyword matches
        scores = {}
        
        for doc_type, type_config in doc_types.items():
            keywords = type_config.get("keywords", [])
            score = 0
            
            for keyword in keywords:
                keyword_pattern = keyword.replace(".", r"\.").replace("&", r"\&")
                if re.search(keyword_pattern, text_lower):
                    score += 1
            
            if score > 0:
                scores[doc_type] = score
        
        # Return the document type with the highest score
        if scores:
            best_match = max(scores.items(), key=lambda x: x[1])
            return best_match[0]
        
        # Fallback logic if no matches found
        return self._fallback_classification(text_lower, year, quarter)
    
    def _fallback_classification(self, text_lower: str, year: Optional[int], quarter: Optional[str]) -> str:
        """Basic fallback classification when no company-specific rules match"""
        
        if any(word in text_lower for word in ['annual', 'year', 'aif']):
            return 'AnnualReport'
        elif any(word in text_lower for word in ['management', 'discussion', 'analysis', 'md']):
            return 'MD&A'
        elif any(word in text_lower for word in ['financial', 'statement']):
            return 'FinancialStatements'
        elif any(word in text_lower for word in ['earnings', 'results', 'release']):
            return 'EarningsRelease'
        elif any(word in text_lower for word in ['circular', 'proxy']):
            return 'ManagementCircular'
        
        return 'Unknown'
    
    def _calculate_confidence(self, url: str, text: str, year: Optional[int], 
                            quarter: Optional[str], doc_type: str, company_name: str) -> float:
        confidence = 0.5
        
        # Year validation based on company configuration
        min_year, max_year = self.get_company_year_range(company_name)
        if year and min_year <= year <= max_year:
            confidence += 0.2
        
        if quarter:
            confidence += 0.1
        
        if doc_type != 'Unknown':
            confidence += 0.2
        
        return min(confidence, 1.0)

class GenericStrategy(ABC):
    def __init__(self, company_name: str, base_url: str, pattern: SitePattern, config_manager: ConfigManager):
        self.company_name = company_name
        self.base_url = base_url
        self.pattern = pattern
        self.config_manager = config_manager
        self.analyzer = DocumentAnalyzer(config_manager)
        # Use new folder structure for company reports
        self.download_base = "Company_Reports"
        self.company_dir = os.path.join(self.download_base, company_name.replace(' ', '_'))
        os.makedirs(self.company_dir, exist_ok=True)
        # For mapping output
        self.mappings = []
    
    def get_browser_headers(self):
        """Get browser headers from configuration"""
        user_agent = self.config_manager.get_global_setting("user_agent", 
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        return {
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://www.google.com/',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'cross-site',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
    
    @abstractmethod
    def execute(self, target_pages: List[str]) -> List[DocumentInfo]:
        pass
    
    def download_document(self, doc_info: DocumentInfo, original_filename: str = None, original_url: str = None, context: str = None) -> bool:
        confidence_threshold = self.config_manager.get_global_setting("confidence_threshold", 0.6)
        min_file_size = self.config_manager.get_global_setting("min_file_size", 1000)
        if doc_info.doc_type == 'Unknown':
            guess_type = self.guess_type_from_context(original_filename, original_url, context)
            if guess_type:
                doc_info.doc_type = guess_type
            else:
                doc_info.doc_type = 'Other'
        if not doc_info.year or doc_info.confidence < confidence_threshold:
            return False
        
        # Check if this is a news release/earnings release that should go to News_Release folder
        is_news_release = self._is_news_release(doc_info, original_url, context)
        
        year_str = str(doc_info.year) if doc_info.year else "UnknownYear"
        
        if is_news_release:
            # Use News_Release folder structure for news releases
            year_dir = os.path.join("News_Release", self.company_name.replace(' ', '_'), year_str)
        else:
            # Use Company_Reports folder structure for regular financial documents
            year_dir = os.path.join(self.download_base, self.company_name.replace(' ', '_'), year_str)
        
        os.makedirs(year_dir, exist_ok=True)
        filename_pattern = self.config_manager.get_filename_pattern()
        # Avoid None in filename
        safe_quarter = doc_info.quarter if doc_info.quarter else ""
        safe_doc_type = doc_info.doc_type if doc_info.doc_type else "Other"
        filename = filename_pattern.format(
            company=self.company_name.replace(' ', '_'),
            document_type=safe_doc_type,
            year=year_str,
            quarter=safe_quarter
        )
        filepath = os.path.join(year_dir, filename)
        if os.path.exists(filepath):
            return True
        try:
            timeout = self.config_manager.get_global_setting("timeout_seconds", 30)
            print(f"Downloading: {filename}")
            response = requests.get(doc_info.url, headers=self.get_browser_headers(), timeout=timeout)
            response.raise_for_status()
            with open(filepath, 'wb') as f:
                f.write(response.content)
            if os.path.getsize(filepath) > min_file_size:
                mapping_entry = {
                    "original_filename": original_filename or os.path.basename(doc_info.url),
                    "original_url": original_url or doc_info.url,
                    "company": self.company_name,
                    "file_type": doc_info.doc_type,
                    "standardized_filename": filename,
                    "year": year_str,
                    "quarter": safe_quarter,
                    "download_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "folder_type": "News_Release" if is_news_release else "Company_Reports"
                }
                self.mappings.append(mapping_entry)
                folder_type = "News_Release" if is_news_release else "Company_Reports"
                print(f"Downloaded: {filename} (saved to {folder_type})")
                return True
            else:
                os.remove(filepath)
                return False
        except Exception as e:
            return False

    def _is_news_release(self, doc_info: DocumentInfo, original_url: str = None, context: str = None) -> bool:
        """Determine if this document should be categorized as a news release"""
        
        # Check document type
        if doc_info.doc_type in ['EarningsRelease', 'NewsRelease', 'PressRelease']:
            return True
        
        # Check URL patterns for news releases
        url_to_check = original_url or doc_info.url
        if url_to_check:
            url_lower = url_to_check.lower()
            news_url_patterns = [
                'news-release', 'news_release', 'newsrelease',
                'press-release', 'press_release', 'pressrelease',
                'earnings', 'results', 'investor-news', 'investor_news'
            ]
            if any(pattern in url_lower for pattern in news_url_patterns):
                return True
        
        # Check if the URL matches company's news_releases_url configuration
        # This is now more aggressive - if the PDF was found on the news releases page, treat it as news
        company_config = self.config_manager.get_company_config(self.company_name)
        if company_config and company_config.get("news_releases_url"):
            news_url = company_config["news_releases_url"].lower()
            if url_to_check and news_url in url_to_check.lower():
                print(f"[DEBUG] Found PDF from news releases page: {url_to_check}")
                return True
        
        # Additional check: if we're processing the news releases URL in the strategy
        if hasattr(self, 'current_page_url'):
            page_url = getattr(self, 'current_page_url', '').lower()
            if company_config and company_config.get("news_releases_url"):
                news_url = company_config["news_releases_url"].lower()
                if news_url in page_url:
                    print(f"[DEBUG] PDF found while processing news releases page: {page_url}")
                    return True
        
        # Check context and title for news-related keywords
        text_to_check = f"{doc_info.title} {context or ''}".lower()
        news_keywords = [
            'news release', 'press release', 'earnings release',
            'quarterly results', 'financial results', 'earnings call',
            'investor update', 'business update'
        ]
        if any(keyword in text_to_check for keyword in news_keywords):
            return True
        
        return False

    def guess_type_from_context(self, original_filename, original_url, context):
        # Combine all possible sources
        haystack = ' '.join([str(x).lower() for x in [original_filename, original_url, context] if x])
        # Heuristic keyword mapping
        type_keywords = [
            (['md&a', 'mda', 'management discussion'], 'MD&A'),
            (['financial', 'statement', 'report to shareholders', 'interim'], 'FinancialStatements'),
            (['annual information form', 'aif'], 'AnnualReport'),
            (['annual report'], 'AnnualReport'),
            (['management information circular', 'proxy', 'circular'], 'ManagementCircular'),
            (['estma', 'extractive'], 'ESTMAReport'),
            (['supplemental'], 'SupplementalDisclosure'),
            (['slavery'], 'SlaveryReport'),
            (['earnings', 'results', 'news release'], 'EarningsRelease'),
        ]
        for keywords, dtype in type_keywords:
            for kw in keywords:
                if kw in haystack:
                    return dtype
        return None

class HybridStrategy(GenericStrategy):
    def execute(self, target_pages: List[str]) -> List[DocumentInfo]:
        strategies = [
            SimpleListStrategy(self.company_name, self.base_url, self.pattern, self.config_manager),
            TableBasedStrategy(self.company_name, self.base_url, self.pattern, self.config_manager),
            CategorizedStrategy(self.company_name, self.base_url, self.pattern, self.config_manager),
            NewsDrillDownStrategy(self.company_name, self.base_url, self.pattern, self.config_manager),
        ]
        
        all_documents = []
        for strategy in strategies:
            try:
                documents = strategy.execute(target_pages)
                all_documents.extend(documents)
            except Exception as e:
                pass
        
        # Remove duplicates based on URL
        unique_docs = {doc.url: doc for doc in all_documents}.values()
        return list(unique_docs)

class SimpleListStrategy(GenericStrategy):
    def execute(self, target_pages: List[str]) -> List[DocumentInfo]:
        documents = []
        company_config = self.config_manager.get_company_config(self.company_name)
        delay = company_config.get("delay_between_requests", 1) if company_config else 1
        for page_url in target_pages:
            try:
                timeout = self.config_manager.get_global_setting("timeout_seconds", 15)
                response = requests.get(page_url, headers=self.get_browser_headers(), timeout=timeout)
                response.raise_for_status()
                soup = BeautifulSoup(response.text, 'html.parser')
                page_title = soup.find('title')
                page_title_text = page_title.get_text() if page_title else ""
                pdf_links = soup.find_all('a', href=lambda x: x and '.pdf' in x.lower())
                for link in pdf_links:
                    href = link.get('href', '')
                    text = link.get_text(strip=True)
                    full_url = urljoin(page_url, href)
                    parent = link.find_parent(['li', 'tr', 'div', 'p'])
                    context = parent.get_text(strip=True) if parent else ""
                    full_context = f"{page_title_text} {context}"
                    doc_info = self.analyzer.analyze_document(full_url, text, full_context, self.company_name)
                    doc_info.url = full_url
                    min_year, max_year = self.analyzer.get_company_year_range(self.company_name)
                    if doc_info.year and min_year <= doc_info.year <= max_year:
                        documents.append(doc_info)
                        if self.download_document(doc_info, context=full_context):
                            time.sleep(delay)
            except Exception as e:
                pass
        return documents

class TableBasedStrategy(GenericStrategy):
    def execute(self, target_pages: List[str]) -> List[DocumentInfo]:
        documents = []
        company_config = self.config_manager.get_company_config(self.company_name)
        delay = company_config.get("delay_between_requests", 1) if company_config else 1
        timeout = self.config_manager.get_global_setting("timeout_seconds", 15)
        use_playwright_table_parsing = company_config.get("use_playwright_table_parsing", False) if company_config else False
        if use_playwright_table_parsing:
            if not PLAYWRIGHT_AVAILABLE:
                return documents
            with sync_playwright() as p:
                browser = p.chromium.launch(headless=True)
                page = browser.new_page()
                for page_url in target_pages:
                    try:
                        page.wait_for_selector('table', timeout=timeout*1000)
                        tables = page.query_selector_all('table')
                        for table_idx, table in enumerate(tables):
                            header_row = table.query_selector('tr')
                            headers = [th.inner_text().strip() for th in header_row.query_selector_all('th')]
                            rows = table.query_selector_all('tr')[1:] if len(headers) > 0 else table.query_selector_all('tr')
                            for row_idx, row in enumerate(rows):
                                cells = row.query_selector_all('td,th')
                                if not cells:
                                    continue
                                period = cells[0].inner_text().strip()
                                for col_idx, cell in enumerate(cells[1:], start=1):
                                    year = headers[col_idx] if col_idx < len(headers) else None
                                    pdf_links = cell.query_selector_all('a[href$=\".pdf\"]')
                                    for link in pdf_links:
                                        href = link.get_attribute('href')
                                        text = link.inner_text().strip()
                                        full_url = urljoin(page_url, href)
                                        context = f"period: {period} | year: {year} | cell: {cell.inner_text().strip()}"
                                        doc_info = self.analyzer.analyze_document(full_url, text, context, self.company_name)
                                        doc_info.url = full_url
                                        try:
                                            doc_info.year = int(year) if year and year.isdigit() else doc_info.year
                                        except Exception:
                                            pass
                                        doc_info.quarter = self.analyzer._extract_quarter(period)
                                        min_year, max_year = self.analyzer.get_company_year_range(self.company_name)
                                        if doc_info.year and min_year <= doc_info.year <= max_year:
                                            documents.append(doc_info)
                                            if self.download_document(doc_info, context=context):
                                                time.sleep(delay)
                    except Exception as e:
                        news_strategy = NewsDrillDownStrategy(self.company_name, self.base_url, self.pattern, self.config_manager)
                        docs = news_strategy.execute([page_url])
                        documents.extend(docs)
                        continue
                browser.close()
            return documents
        for page_url in target_pages:
            try:
                response = requests.get(page_url, headers=self.get_browser_headers(), timeout=timeout)
                response.raise_for_status()
                soup = BeautifulSoup(response.text, 'html.parser')
                page_title_text = soup.find('title').get_text(strip=True) if soup.find('title') else ""
                tables = soup.find_all('table')
                for table in tables:
                    header_text = ' '.join(th.get_text(strip=True) for th in table.find_all('th'))
                    pdf_links = table.find_all('a', href=lambda x: x and '.pdf' in x.lower())
                    for link in pdf_links:
                        href = link.get('href', '')
                        text = link.get_text(strip=True)
                        row = link.find_parent('tr')
                        row_text = ' '.join(td.get_text(strip=True) for td in row.find_all(['td', 'th'])) if row else ""
                        full_context = f"{page_title_text} {header_text} {row_text}"
                        full_url = urljoin(page_url, href)
                        doc_info = self.analyzer.analyze_document(full_url, text, full_context, self.company_name)
                        doc_info.url = full_url
                        min_year, max_year = self.analyzer.get_company_year_range(self.company_name)
                        if doc_info.year and min_year <= doc_info.year <= max_year:
                            documents.append(doc_info)
                            if self.download_document(doc_info, context=full_context):
                                time.sleep(delay)
            except Exception as e:
                pass
        return documents

class NewsDrillDownStrategy(GenericStrategy):
    def execute(self, target_pages: List[str]) -> List[DocumentInfo]:
        documents = []
        company_config = self.config_manager.get_company_config(self.company_name)
        doc_types = company_config.get("document_types", {}) if company_config else {}
        earnings_keywords = []
        for doc_type, config in doc_types.items():
            if 'earnings' in doc_type.lower() or 'release' in doc_type.lower():
                earnings_keywords.extend(config.get("keywords", []))
        if not earnings_keywords:
            earnings_keywords = ['earnings', 'financial results', 'quarterly results']
        delay = company_config.get("delay_between_requests", 1) if company_config else 1
        for page_url in target_pages:
            try:
                timeout = self.config_manager.get_global_setting("timeout_seconds", 15)
                response = requests.get(page_url, headers=self.get_browser_headers(), timeout=timeout)
                response.raise_for_status()
                soup = BeautifulSoup(response.text, 'html.parser')
                news_links = soup.find_all('a', href=True)
                for link in news_links:
                    text = link.get_text(strip=True).lower()
                    href = link.get('href', '')
                    if any(keyword in text for keyword in earnings_keywords):
                        news_url = urljoin(page_url, href)
                        try:
                            time.sleep(delay)
                            news_response = requests.get(news_url, headers=self.get_browser_headers(), timeout=timeout)
                            if news_response.status_code == 200:
                                news_soup = BeautifulSoup(news_response.text, 'html.parser')
                                pdf_links = news_soup.find_all('a', href=lambda x: x and '.pdf' in x.lower())
                                for pdf_link in pdf_links:
                                    pdf_href = pdf_link.get('href', '')
                                    pdf_text = pdf_link.get_text(strip=True)
                                    pdf_url = urljoin(news_url, pdf_href)
                                    doc_info = self.analyzer.analyze_document(pdf_url, text + ' ' + pdf_text, "", self.company_name)
                                    doc_info.url = pdf_url
                                    doc_info.doc_type = 'EarningsRelease'
                                    min_year, max_year = self.analyzer.get_company_year_range(self.company_name)
                                    if doc_info.year and min_year <= doc_info.year <= max_year:
                                        documents.append(doc_info)
                                        if self.download_document(doc_info, original_filename=os.path.basename(pdf_url), original_url=pdf_url, context=text + ' ' + pdf_text):
                                            break
                        except Exception as e:
                            pass
            except Exception as e:
                pass
        return documents

class CategorizedStrategy(GenericStrategy):
    def execute(self, target_pages: List[str]) -> List[DocumentInfo]:
        documents = []
        company_config = self.config_manager.get_company_config(self.company_name)
        delay = company_config.get("delay_between_requests", 1) if company_config else 1
        for page_url in target_pages:
            try:
                timeout = self.config_manager.get_global_setting("timeout_seconds", 15)
                response = requests.get(page_url, headers=self.get_browser_headers(), timeout=timeout)
                response.raise_for_status()
                soup = BeautifulSoup(response.text, 'html.parser')
                categories = soup.find_all(class_=re.compile(r'tab|category|section', re.I))
                if not categories:
                    categories = [soup]
                for category in categories:
                    pdf_links = category.find_all('a', href=lambda x: x and '.pdf' in x.lower())
                    for link in pdf_links:
                        href = link.get('href', '')
                        text = link.get_text(strip=True)
                        full_url = urljoin(page_url, href)
                        doc_info = self.analyzer.analyze_document(full_url, text, "", self.company_name)
                        doc_info.url = full_url
                        min_year, max_year = self.analyzer.get_company_year_range(self.company_name)
                        if doc_info.year and min_year <= doc_info.year <= max_year:
                            documents.append(doc_info)
                            if self.download_document(doc_info, original_filename=os.path.basename(full_url), original_url=full_url, context=""):
                                time.sleep(delay)
            except Exception as e:
                pass
        return documents

class SiteAnalyzer:
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.financial_keywords = [
            'investor', 'financial', 'annual', 'quarterly', 'earnings',
            'report', 'statement', 'filing', 'disclosure'
        ]
    
    def find_document_pages(self, base_url: str) -> List[str]:
        document_pages = []
        
        candidate_patterns = [
            "/investors", "/investor", "/investor-relations",
            "/financial", "/financials", "/reports",
            "/news", "/news-releases", "/press-releases",
            "/documents", "/ir"
        ]
        
        candidates = [base_url]
        for pattern in candidate_patterns:
            candidates.append(urljoin(base_url, pattern))
            candidates.append(urljoin(base_url, pattern + "/"))
        
        for url in candidates:
            if self._is_document_page(url):
                document_pages.append(url)
        
        return document_pages if document_pages else [base_url]
    
    def _is_document_page(self, url: str) -> bool:
        try:
            user_agent = self.config_manager.get_global_setting("user_agent")
            headers = {
                'User-Agent': user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Referer': 'https://www.google.com/'
            }
            
            timeout = self.config_manager.get_global_setting("timeout_seconds", 10)
            response = requests.get(url, headers=headers, timeout=timeout)
            if response.status_code != 200:
                return False
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            pdf_links = soup.find_all('a', href=lambda x: x and '.pdf' in x.lower())
            if len(pdf_links) >= 2:
                return True
            
            text_content = soup.get_text().lower()
            keyword_count = sum(1 for keyword in self.financial_keywords if keyword in text_content)
            if keyword_count >= 3:
                return True
            
            return False
            
        except Exception:
            return False

class SeleniumStrategy(GenericStrategy):
    def execute(self, target_pages: List[str]) -> List[DocumentInfo]:
        documents = []
        if not SELENIUM_AVAILABLE:
            return documents
        # You can customize options as needed
        options = webdriver.ChromeOptions()
        options.add_argument('--headless')
        driver = webdriver.Chrome(options=options)
        company_config = self.config_manager.get_company_config(self.company_name)
        delay = company_config.get("delay_between_requests", 1) if company_config else 1
        
        for page_url in target_pages:
            # Track current page URL for news release detection
            self.current_page_url = page_url
            
            try:
                driver.get(page_url)
                time.sleep(delay * 2)  # Give more time for initial load
                
                # Check if this is a news releases page
                is_news_page = self._is_news_releases_page(page_url)
                
                # Wait for page content to load and debug what we can find
                try:
                    from selenium.webdriver.support.ui import WebDriverWait
                    from selenium.webdriver.support import expected_conditions as EC
                    wait = WebDriverWait(driver, 10)
                    wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                except:
                    pass
                
                # Check what links are actually on the page
                all_links = driver.find_elements(By.TAG_NAME, "a")
                
                # Look for "Read More" links with multiple selectors
                read_more_selectors = [
                    "//a[contains(text(), 'Read More')]",
                    "//a[text()='Read More']", 
                    "//a[contains(@class, 'read-more')]",
                    "//a[contains(@href, 'read-more')]",
                    "//*[contains(text(), 'Read More')]/ancestor-or-self::a",
                    "//a[contains(normalize-space(text()), 'Read More')]"
                ]
                
                read_more_links = []
                for selector in read_more_selectors:
                    try:
                        links_found = driver.find_elements(By.XPATH, selector)
                        if links_found:
                            read_more_links.extend(links_found)
                            break  # Use the first selector that works
                    except Exception as e:
                        continue
                
                # Remove duplicates
                read_more_links = list(set(read_more_links))
                
                                # If still no Read More links, try searching for any links with common news release patterns
                if not read_more_links:
                    news_link_patterns = [
                        "//a[contains(@href, 'news')]",
                        "//a[contains(@href, 'release')]", 
                        "//a[contains(@href, 'results')]",
                        "//a[contains(@href, 'reports')]"
                    ]
                    
                    for pattern in news_link_patterns:
                        try:
                            pattern_links = driver.find_elements(By.XPATH, pattern)
                            if len(pattern_links) > 0:
                                read_more_links.extend(pattern_links[:10])  # Take first 10
                                break
                        except:
                            continue
                
                # If STILL no links, try looking for clickable elements with news-related text
                if not read_more_links:
                    text_patterns = [
                        "//*[contains(text(), 'Q1') or contains(text(), 'Q2') or contains(text(), 'Q3') or contains(text(), 'Q4')]//a",
                        "//*[contains(text(), '2024') or contains(text(), '2025')]//a",
                        "//*[contains(text(), 'Results') or contains(text(), 'Update')]//a",
                        "//h3//following-sibling::a | //h3//a",
                        "//div[contains(@class, 'news')]//a"
                    ]
                    
                    for pattern in text_patterns:
                        try:
                            text_links = driver.find_elements(By.XPATH, pattern)
                            if text_links:
                                read_more_links.extend(text_links[:5])  # Take first 5
                                break
                        except:
                            continue
                
                # First, look for direct PDF links
                links = driver.find_elements(By.XPATH, "//a[contains(@href, '.pdf')]")
                
                for link in links:
                    try:
                        href = link.get_attribute('href')
                        text = link.text
                        full_url = urljoin(page_url, href)
                        doc_info = self.analyzer.analyze_document(full_url, text, f"Page: {page_url}", self.company_name)
                        doc_info.url = full_url
                        min_year, max_year = self.analyzer.get_company_year_range(self.company_name)
                        if doc_info.year and min_year <= doc_info.year <= max_year:
                            documents.append(doc_info)
                            if self.download_document(doc_info, original_url=full_url, context=f"Found on: {page_url}"):
                                time.sleep(delay)
                    except Exception as e:
                        continue
                
                # If this is a news page, try drilling down into news release links
                if is_news_page:
                    # Collect URLs first to avoid stale element issues
                    detail_urls = []
                    for link in read_more_links[:10]:  # Limit to first 10 to avoid excessive processing
                        try:
                            detail_url = link.get_attribute('href')
                            link_text = link.text.strip()
                            if detail_url and detail_url != page_url:  # Avoid self-referencing links
                                detail_urls.append((detail_url, link_text))
                        except Exception as e:
                            continue
                    
                    # Process each detail page
                    for detail_url, link_text in detail_urls:
                        try:
                            driver.get(detail_url)
                            time.sleep(delay)
                            
                            # Look for PDFs on the detail page
                            detail_pdf_links = driver.find_elements(By.XPATH, "//a[contains(@href, '.pdf')]")
                            
                            for pdf_link in detail_pdf_links:
                                try:
                                    pdf_href = pdf_link.get_attribute('href')
                                    pdf_text = pdf_link.text or 'News Release PDF'
                                    pdf_full_url = urljoin(detail_url, pdf_href)
                                    
                                    doc_info = self.analyzer.analyze_document(
                                        pdf_full_url, 
                                        f"{link_text} - {pdf_text}", 
                                        f"News detail: {link_text} from {detail_url}", 
                                        self.company_name
                                    )
                                    doc_info.url = pdf_full_url
                                    
                                    # Force classification as news release if found on news page
                                    if is_news_page and doc_info.doc_type not in ['EarningsRelease', 'NewsRelease']:
                                        doc_info.doc_type = 'NewsRelease'
                                    
                                    min_year, max_year = self.analyzer.get_company_year_range(self.company_name)
                                    if doc_info.year and min_year <= doc_info.year <= max_year:
                                        documents.append(doc_info)
                                        if self.download_document(
                                            doc_info, 
                                            original_url=pdf_full_url, 
                                            context=f"News release from: {page_url}"
                                        ):
                                            time.sleep(delay)
                                except Exception as e:
                                    continue
                            
                            # Navigate back to main news page
                            driver.get(page_url)
                            time.sleep(delay)
                            
                        except Exception as e:
                            continue
                            
            except Exception as e:
                pass
        
        driver.quit()
        return documents
    
    def _is_news_releases_page(self, page_url: str) -> bool:
        """Check if the current page is a news releases page"""
        company_config = self.config_manager.get_company_config(self.company_name)
        if company_config and company_config.get("news_releases_url"):
            news_url = company_config["news_releases_url"].lower()
            return news_url in page_url.lower()
        
        # Fallback check for common news release URL patterns
        return any(pattern in page_url.lower() for pattern in ['news-release', 'news_release', 'press-release'])

class MEGNewsStrategy(GenericStrategy):
    def __init__(self, company_name: str, base_url: str, pattern: SitePattern, config_manager: ConfigManager):
        super().__init__(company_name, base_url, pattern, config_manager)
        self.meg_config = self._load_meg_config()
    
    def _load_meg_config(self):
        try:
            with open('meg_news_config.json', 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return {}
    
    def execute(self, target_pages: List[str]) -> List[DocumentInfo]:
        documents = []
        if not SELENIUM_AVAILABLE:
            return documents
        
        options = webdriver.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        driver = webdriver.Chrome(options=options)
        downloaded_pdfs = set()
        try:
            meg_config = self.meg_config.get('meg_energy_news', {})
            years = meg_config.get('years', {})
            scraping_config = meg_config.get('scraping_config', {})
            delay = scraping_config.get('delay_between_requests', 2)
            
            for year, year_data in years.items():
                year_url = year_data.get('url')
                if not year_url:
                    continue
                try:
                    driver.get(year_url)
                    time.sleep(delay)
                    # Collect all detail URLs and titles first (avoid stale elements)
                    read_more_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'Read More') or contains(@href, 'news-release-detail')]")
                    detail_urls = []
                    for link in read_more_links:
                        detail_url = link.get_attribute('href')
                        title = link.text.strip()
                        if detail_url and 'news-release-detail' in detail_url:
                            detail_urls.append((detail_url, title))
                    for detail_url, title in detail_urls:
                        try:
                            import re
                            id_match = re.search(r'id=(\d+)', detail_url)
                            if not id_match:
                                continue
                            news_id = id_match.group(1)
                            driver.get(detail_url)
                            time.sleep(delay)
                            pdf_links = driver.find_elements(By.XPATH, "//a[contains(@href, '.pdf')]")
                            for pdf_link in pdf_links:
                                pdf_url = pdf_link.get_attribute('href')
                                pdf_text = pdf_link.text or pdf_link.get_attribute('title') or 'MEG News Release'
                                if pdf_url and '.pdf' in pdf_url.lower():
                                    if pdf_url in downloaded_pdfs:
                                        continue
                                    downloaded_pdfs.add(pdf_url)
                                    year_int = int(year) if year.isdigit() else None
                                    doc_info = self.analyzer.analyze_document(
                                        pdf_url, 
                                        pdf_text, 
                                        f"MEG Energy News Release {year}", 
                                        self.company_name
                                    )
                                    doc_info.url = pdf_url
                                    doc_info.year = year_int
                                    doc_info.doc_type = 'EarningsRelease'
                                    if year_int and 2019 <= year_int <= 2025:
                                        documents.append(doc_info)
                                        if self.download_document(
                                            doc_info, 
                                            original_filename=f"MEG_News_{news_id}_{year}.pdf",
                                            original_url=pdf_url,
                                            context=f"News Release ID: {news_id}, Year: {year}"
                                        ):
                                            pass
                            driver.get(year_url)
                            time.sleep(delay)
                        except Exception as e:
                            continue
                except Exception as e:
                    continue
        finally:
            driver.quit()
        return documents

class UniversalScraper:
    def __init__(self, config_file: str = "companies_config.json"):
        self.config_manager = ConfigManager(config_file)
        self.pattern_detector = PatternDetector(self.config_manager)
        self.site_analyzer = SiteAnalyzer(self.config_manager)
        
        self.strategies = {
            'simple_list': SimpleListStrategy,
            'table_based': TableBasedStrategy,
            'news_drill_down': NewsDrillDownStrategy,
            'categorized': CategorizedStrategy,
            'hybrid': HybridStrategy,
            'selenium': SeleniumStrategy,
            'meg_news': MEGNewsStrategy  # Add MEG News strategy
        }
    
    def analyze_and_scrape_with_config(self, company_name: str, urls: List[str], scraper_type: str) -> Dict[str, Any]:
        start_time = time.time()
        
        try:
            # Use configured scraper type
            if scraper_type in self.strategies:
                pattern_type = scraper_type
                pattern = SitePattern(pattern_type, 1.0, {}, {'requires_js': False})
            else:
                pattern_type = 'simple_list'
                pattern = SitePattern(pattern_type, 0.5, {}, {'requires_js': False})
            
            strategy_class = self.strategies[pattern.pattern_type]
            strategy = strategy_class(company_name, urls[0] if urls else "", pattern, self.config_manager)
            
            documents = strategy.execute(urls)
            
            result = {
                'company': company_name,
                'urls_processed': urls,
                'pattern_detected': pattern.pattern_type,
                'pattern_confidence': pattern.confidence,
                'pages_analyzed': len(urls),
                'documents_found': len(documents),
                'documents_downloaded': len([d for d in documents if d.confidence >= self.config_manager.get_global_setting("confidence_threshold", 0.6)]),
                'years_covered': list(set(d.year for d in documents if d.year)),
                'processing_time': time.time() - start_time
            }
            
            return result
            
        except Exception as e:
            return {'error': str(e), 'processing_time': time.time() - start_time}
    
    def analyze_and_scrape(self, company_name: str, base_url: str) -> Dict[str, Any]:
        start_time = time.time()
        max_analysis_time = self.config_manager.get_global_setting("max_analysis_time", 120)
        
        try:
            document_pages = self.site_analyzer.find_document_pages(base_url)
            
            if time.time() - start_time > max_analysis_time:
                return {'timeout': True, 'stage': 'analysis'}
            
            if document_pages:
                try:
                    user_agent = self.config_manager.get_global_setting("user_agent")
                    headers = {
                        'User-Agent': user_agent,
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.9',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Referer': 'https://www.google.com/',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'cross-site'
                    }
                    timeout = self.config_manager.get_global_setting("timeout_seconds", 10)
                    response = requests.get(document_pages[0], headers=headers, timeout=timeout)
                    soup = BeautifulSoup(response.text, 'html.parser')
                    pattern = self.pattern_detector.detect_pattern(document_pages[0], soup)
                except Exception as e:
                    pattern = SitePattern('simple_list', 0.5, {'pdf_links': 'a[href$=".pdf"]'}, {'requires_js': False})
            else:
                pattern = SitePattern('simple_list', 0.5, {'pdf_links': 'a[href$=".pdf"]'}, {'requires_js': False})
            
            if time.time() - start_time > max_analysis_time:
                return {'timeout': True, 'stage': 'pattern_detection'}
            
            strategy_class = self.strategies[pattern.pattern_type]
            strategy = strategy_class(company_name, base_url, pattern, self.config_manager)
            
            documents = strategy.execute(document_pages[:3])
            
            result = {
                'company': company_name,
                'base_url': base_url,
                'pattern_detected': pattern.pattern_type,
                'pattern_confidence': pattern.confidence,
                'pages_analyzed': len(document_pages),
                'documents_found': len(documents),
                'documents_downloaded': len([d for d in documents if d.confidence >= self.config_manager.get_global_setting("confidence_threshold", 0.6)]),
                'years_covered': list(set(d.year for d in documents if d.year)),
                'processing_time': time.time() - start_time
            }
            
            return result
            
        except Exception as e:
            return {'error': str(e), 'processing_time': time.time() - start_time}
    
    def bulk_scrape(self, companies: List[Tuple[str, List[str], str]]) -> Dict[str, Any]:
        results = {}
        total_docs = 0
        for i, (company_name, urls, scraper_type) in enumerate(companies, 1):
            start_time = time.time()
            max_company_time = self.config_manager.get_global_setting("max_company_time", 120)
            strategy = None
            documents = []
            try:
                if scraper_type in self.strategies:
                    pattern_type = scraper_type
                    pattern = SitePattern(pattern_type, 1.0, {}, {'requires_js': False})
                else:
                    pattern_type = 'simple_list'
                    pattern = SitePattern(pattern_type, 0.5, {}, {'requires_js': False})
                strategy_class = self.strategies[pattern_type]
                strategy = strategy_class(company_name, urls[0] if urls else "", pattern, self.config_manager)
                documents = strategy.execute(urls)
                result = {
                    'company': company_name,
                    'urls_processed': urls,
                    'pattern_detected': pattern.pattern_type,
                    'pattern_confidence': pattern.confidence,
                    'pages_analyzed': len(urls),
                    'documents_found': len(documents),
                    'documents_downloaded': len([d for d in documents if d.confidence >= self.config_manager.get_global_setting("confidence_threshold", 0.6)]),
                    'years_covered': list(set(d.year for d in documents if d.year)),
                    'processing_time': time.time() - start_time
                }
                results[company_name] = result
                if 'documents_downloaded' in result:
                    total_docs += result['documents_downloaded']
            except KeyboardInterrupt:
                break
            except Exception as e:
                results[company_name] = {'error': str(e)}
                time.sleep(2)
            finally:
                if strategy is not None:
                    mappings_file = f"{company_name.replace(' ', '').replace('_', '')}_mappings.json"
                    mapping_data = strategy.mappings
                    if not mapping_data:
                        mapping_data = [{"message": "No files were downloaded for this company in this run."}]
                    with open(mappings_file, "w", encoding="utf-8") as mf:
                        json.dump(mapping_data, mf, indent=2, ensure_ascii=False)
                if i < len(companies):
                    time.sleep(2)
        return results

def load_companies_from_config(config_file: str = "companies_config.json") -> List[Tuple[str, List[str], str]]:
    """Load companies from configuration file"""
    config_manager = ConfigManager(config_file)
    companies = []
    
    for company_name, company_config in config_manager.get_all_companies().items():
        display_name = company_config.get("display_name", company_name)
        scraper_type = company_config.get("scraper_type", "simple_list")
        
        # Collect specific URLs
        urls = []
        if company_config.get("financial_reports_url"):
            urls.append(company_config["financial_reports_url"])
        if company_config.get("news_releases_url"):
            urls.append(company_config["news_releases_url"])
        
        # Fallback to base_url if no specific URLs
        if not urls:
            urls.append(company_config.get("base_url", ""))
        
        companies.append((display_name, urls, scraper_type))
    
    return companies

def add_company(name: str, urls: List[str], scraper_type: str = "simple_list", config_file: str = "companies_config.json"):
    """Add a new company for scraping"""
    scraper = UniversalScraper(config_file)
    result = scraper.analyze_and_scrape_with_config(name, urls, scraper_type)
    
    return result

def fix_unknown_classifications(config_file: str = "companies_config.json", company_dir: str = None):
    """Re-classify and rename files that were marked as 'Unknown'"""
    
    config_manager = ConfigManager(config_file)
    analyzer = DocumentAnalyzer(config_manager)
    
    # Load download directory from config if not provided
    if company_dir is None:
        company_dir = config_manager.get_global_setting("download_directory", "company_reports")
    
    if not os.path.exists(company_dir):
        return
    
    total_fixed = 0
    
    for company_folder in os.listdir(company_dir):
        company_path = os.path.join(company_dir, company_folder)
        if not os.path.isdir(company_path):
            continue
            
        unknown_files = [f for f in os.listdir(company_path) if "_Unknown_" in f]
        
        for filename in unknown_files:
            filepath = os.path.join(company_path, filename)
            
            parts = filename.replace('.pdf', '').split('_')
            if len(parts) >= 4:
                company = parts[0] + '_' + parts[1] 
                year = parts[3]
                quarter = parts[4]
                
                doc_info = analyzer.analyze_document(filepath, filename, f"{company} {year} {quarter}", company_folder)
                
                if doc_info.doc_type != 'Unknown':
                    filename_pattern = config_manager.get_filename_pattern()
                    new_filename = filename_pattern.format(
                        company=company,
                        document_type=doc_info.doc_type,
                        year=year,
                        quarter=quarter
                    )
                    new_filepath = os.path.join(company_path, new_filename)
                    
                    if not os.path.exists(new_filepath):
                        os.rename(filepath, new_filepath)
                        total_fixed += 1
                    else:
                        os.remove(filepath)
                        total_fixed += 1
    
    return total_fixed

def download_all_meg_news_pdfs(config_path='meg_news_config.json', company_name='MEG Energy', companies_config_path='companies_config.json'):
    

    # Load configs
    with open(config_path, 'r', encoding='utf-8') as f:
        news_config = json.load(f)
    with open(companies_config_path, 'r', encoding='utf-8') as f:
        companies_config = json.load(f)
    pattern = companies_config.get('filename_standards', {}).get('pattern', '{company}_{document_type}_{year}_{quarter}.pdf')
    # Use unified News_Release folder structure (consistent with main scraper)
    download_base = 'News_Release'
    download_dir = os.path.join(download_base, company_name.replace(' ', '_'))
    os.makedirs(download_dir, exist_ok=True)
    pdf_set = set()
    meg_news = news_config.get('meg_energy_news', {})
    years = meg_news.get('years', {})
    for year, year_data in years.items():
        news_releases = year_data.get('news_releases', {})
        for news_id, news_data in news_releases.items():
            for pdf_url in news_data.get('pdf_urls', []):
                pdf_set.add((year, news_id, pdf_url, news_data.get('title', '')))
    for year, news_id, pdf_url, title in pdf_set:
        try:
            parsed = urlparse(pdf_url)
            document_type = "EarningsRelease"
            # Use short, smart name for news release
            short_title = title.strip().replace(' ', '_')[:30] if title and title != 'Read More' else ''
            filename = f"{company_name.replace(' ', '_')}_{year}_{news_id}{'_' + short_title if short_title else ''}.pdf"
            # Place in News_Release/{company}/{year}/
            year_dir = os.path.join(download_base, company_name.replace(' ', '_'), year)
            os.makedirs(year_dir, exist_ok=True)
            filepath = os.path.join(year_dir, filename)
            if os.path.exists(filepath):
                continue
            print(f"Downloading: {filename}")
            resp = requests.get(pdf_url, timeout=60)
            resp.raise_for_status()
            with open(filepath, 'wb') as f:
                f.write(resp.content)
            print(f"Downloaded: {filename}")
        except Exception as e:
            pass

if __name__ == "__main__":
    import sys
    scraper = UniversalScraper()
    companies = load_companies_from_config()
    for company, urls, scraper_type in companies:
        result = scraper.analyze_and_scrape_with_config(company, urls, scraper_type)
        if company.lower().replace(' ', '') == 'megenergy':
            download_all_meg_news_pdfs()
